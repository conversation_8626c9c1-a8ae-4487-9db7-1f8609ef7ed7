#!/usr/bin/env python3
"""
Test script to verify that the minimap position drift issue is fixed.
This script simulates rapid floor switching to check for position drift.
"""

import sys
import time
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import QTimer, QPointF
from PyQt6.QtGui import QKeyEvent
from PyQt6.QtCore import Qt

# Import the minimap viewer
from minimap_viewer import MinimapViewer


class DriftTestWindow(QMainWindow):
    """Test window for checking minimap position drift."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimap Drift Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Add instructions
        instructions = QLabel(
            "Instructions:\n"
            "1. Wait for the minimap to load\n"
            "2. Note the initial position of the minimap\n"
            "3. Click 'Start Rapid Floor Switching Test' to simulate rapid + and - key presses\n"
            "4. Observe if the minimap position drifts during the test\n"
            "5. The test will switch floors 50 times rapidly\n"
            "6. After the test, the minimap should return to the same position"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Add test button
        self.test_button = QPushButton("Start Rapid Floor Switching Test")
        self.test_button.clicked.connect(self.start_drift_test)
        layout.addWidget(self.test_button)
        
        # Add status label
        self.status_label = QLabel("Ready to test")
        layout.addWidget(self.status_label)
        
        # Create minimap viewer
        self.minimap_viewer = MinimapViewer()
        layout.addWidget(self.minimap_viewer, 1)
        
        # Test variables
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.perform_floor_switch)
        self.test_count = 0
        self.max_test_count = 50
        self.initial_center = None
        self.initial_zoom = None
        
    def start_drift_test(self):
        """Start the rapid floor switching test."""
        self.test_button.setEnabled(False)
        self.test_count = 0
        
        # Save initial position
        self.initial_center = self.minimap_viewer.graphics_view.mapToScene(
            self.minimap_viewer.graphics_view.viewport().rect().center()
        )
        self.initial_zoom = self.minimap_viewer.graphics_view.zoom_factor
        
        self.status_label.setText(f"Starting test... Initial center: {self.initial_center}, zoom: {self.initial_zoom}")
        
        # Start rapid switching (every 100ms)
        self.test_timer.start(100)
        
    def perform_floor_switch(self):
        """Perform a single floor switch."""
        if self.test_count >= self.max_test_count:
            self.finish_test()
            return
            
        # Alternate between floor up and floor down
        if self.test_count % 2 == 0:
            self.minimap_viewer.floor_up()
        else:
            self.minimap_viewer.floor_down()
            
        self.test_count += 1
        self.status_label.setText(f"Test progress: {self.test_count}/{self.max_test_count}")
        
    def finish_test(self):
        """Finish the test and report results."""
        self.test_timer.stop()
        
        # Get final position
        final_center = self.minimap_viewer.graphics_view.mapToScene(
            self.minimap_viewer.graphics_view.viewport().rect().center()
        )
        final_zoom = self.minimap_viewer.graphics_view.zoom_factor
        
        # Calculate drift
        if self.initial_center and not final_center.isNull():
            drift_x = final_center.x() - self.initial_center.x()
            drift_y = final_center.y() - self.initial_center.y()
            drift_distance = (drift_x ** 2 + drift_y ** 2) ** 0.5
            zoom_drift = abs(final_zoom - self.initial_zoom)
            
            result_text = (
                f"Test completed!\n"
                f"Initial center: ({self.initial_center.x():.2f}, {self.initial_center.y():.2f})\n"
                f"Final center: ({final_center.x():.2f}, {final_center.y():.2f})\n"
                f"Position drift: ({drift_x:.2f}, {drift_y:.2f}) - Distance: {drift_distance:.2f}\n"
                f"Initial zoom: {self.initial_zoom:.4f}\n"
                f"Final zoom: {final_zoom:.4f}\n"
                f"Zoom drift: {zoom_drift:.4f}\n"
                f"{'PASS: Minimal drift detected' if drift_distance < 10 and zoom_drift < 0.01 else 'FAIL: Significant drift detected'}"
            )
        else:
            result_text = "Test completed, but could not measure drift (invalid coordinates)"
            
        self.status_label.setText(result_text)
        self.test_button.setEnabled(True)


def main():
    """Main function to run the drift test."""
    app = QApplication(sys.argv)
    
    # Check if minimap directory exists
    minimap_dir = Path("processed_minimap")
    if not minimap_dir.exists():
        print(f"Error: Minimap directory '{minimap_dir}' not found.")
        print("Please ensure the processed minimap images are available.")
        return 1
    
    # Create and show test window
    window = DriftTestWindow()
    window.show()
    
    # Run application
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
